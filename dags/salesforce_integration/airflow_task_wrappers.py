"""
Airflow Task Wrappers - Wrappers para suporte a unidades de negócio
Wraps das funções existentes do etl_main para funcionarem com unidades de negócio específicas.
"""

import logging
from salesforce_integration.business_unit_adapter import (
    create_business_unit_adapter, 
    patch_config_for_business_unit
)

# =============================================================================
# WRAPPERS DE EXTRAÇÃO
# =============================================================================

def airflow_extract_newcon_clients_individual_task(**context):
    """Wrapper para extração de clientes NewCon com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    # Importa função original
    from salesforce_integration.etl_main import airflow_extract_newcon_clients_individual_task as original_func
    return original_func(**context)

def airflow_extract_newcon_products_individual_task(**context):
    """Wrapper para extração de produtos NewCon com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_newcon_products_individual_task as original_func
    return original_func(**context)

def airflow_extract_newcon_leads_individual_task(**context):
    """Wrapper para extração de leads NewCon com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_newcon_leads_individual_task as original_func
    return original_func(**context)

def airflow_extract_newcon_proposals_individual_task(**context):
    """Wrapper para extração de propostas NewCon com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_newcon_proposals_individual_task as original_func
    return original_func(**context)

def airflow_extract_rdstation_leads_individual_task(**context):
    """Wrapper para extração de leads RD Station com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_rdstation_leads_individual_task as original_func
    return original_func(**context)

def airflow_extract_orbbits_origin_individual_task(**context):
    """Wrapper para extração origem Orbbits com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_orbbits_origin_individual_task as original_func
    return original_func(**context)

def airflow_extract_orbbits_payments_individual_task(**context):
    """Wrapper para extração pagamentos Orbbits com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_orbbits_payments_individual_task as original_func
    return original_func(**context)

def airflow_extract_orbbits_sales_individual_task(**context):
    """Wrapper para extração vendas Orbbits com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_orbbits_sales_individual_task as original_func
    return original_func(**context)

def airflow_extract_orbbits_prices_individual_task(**context):
    """Wrapper para extração preços Orbbits com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_orbbits_prices_individual_task as original_func
    return original_func(**context)

def airflow_extract_orbbits_proposals_individual_task(**context):
    """Wrapper para extração propostas Orbbits com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_orbbits_proposals_individual_task as original_func
    return original_func(**context)

def airflow_extract_quiver_clients_individual_task(**context):
    """Wrapper para extração clientes Quiver com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_quiver_clients_individual_task as original_func
    return original_func(**context)

def airflow_extract_quiver_leads_individual_task(**context):
    """Wrapper para extração leads Quiver com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_quiver_leads_individual_task as original_func
    return original_func(**context)

def airflow_extract_quiver_products_individual_task(**context):
    """Wrapper para extração produtos Quiver com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_quiver_products_individual_task as original_func
    return original_func(**context)

def airflow_extract_quiver_proposals_individual_task(**context):
    """Wrapper para extração propostas Quiver com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_extract_quiver_proposals_individual_task as original_func
    return original_func(**context)

# =============================================================================
# WRAPPERS DE CONSOLIDAÇÃO
# =============================================================================

def airflow_consolidate_table_extractions_task(**context):
    """Wrapper para consolidação com suporte a unidades de negócio"""
    import logging

    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    business_unit = adapter.business_unit

    try:
        consolidated_data = {}

        # Lista de todas as tabelas extraídas individualmente
        table_keys = [
            'newcon_clients_individual',
            'newcon_products_individual',
            'newcon_leads_individual',
            'newcon_proposals_individual',
            'rdstation_leads_individual',
            'orbbits_origin_individual',
            'orbbits_payments_individual',
            'orbbits_sales_individual',
            'orbbits_prices_individual',
            'orbbits_proposals_individual',
            'quiver_clients_individual',
            'quiver_leads_individual',
            'quiver_products_individual',
            'quiver_proposals_individual'
        ]

        # Recupera dados de todas as extrações paralelas
        if 'ti' in context:
            ti = context['ti']

            for key in table_keys:
                table_data = ti.xcom_pull(key=key)
                if table_data and table_data.get('success', False):
                    consolidated_data.update(table_data['data'])
                    logging.info(f"✅ Consolidado {key}: {table_data['total_records']} registros")
                else:
                    logging.warning(f"⚠️ Dados não encontrados para {key}")

            # Aplica filtro por business_unit
            logging.info(f"🔧 Aplicando filtro para business_unit: {business_unit}")
            filtered_data = adapter.filter_extraction_data(consolidated_data)

            # Salva dados consolidados e filtrados
            consolidation_result = {
                'success': True,
                'data': filtered_data,
                'total_records': sum(len(df) for df in filtered_data.values() if hasattr(df, '__len__')),
                'tables_count': len(filtered_data),
                'extraction_method': 'table_parallel',
                'business_unit': business_unit
            }

            ti.xcom_push(key='consolidated_table_extraction_result', value=consolidation_result)

            logging.info(f"✅ Consolidação por tabela concluída para {business_unit}: {consolidation_result['total_records']} registros de {consolidation_result['tables_count']} tabelas")
            # Não retorna nada para evitar return_value desnecessário

        else:
            raise ValueError("TaskInstance não encontrada")
    except Exception as e:
        logging.error(f"Erro na consolidação de extrações por tabela: {e}")
        raise

# =============================================================================
# WRAPPERS DE TRANSFORMAÇÃO
# =============================================================================

def airflow_transform_produtos_task(**context):
    """Wrapper para transformação produtos com suporte a unidades de negócio"""
    import pandas as pd
    
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    business_unit = adapter.business_unit
    
    # Usar transformação específica por business unit
    from salesforce_integration.business_unit_transformers import transform_produtos_by_business_unit
    
    try:
        # Recuperar dados consolidados
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_table_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
            
        if consolidated_data is None:
            logging.error("❌ Dados consolidados não encontrados no XCom")
            raise ValueError("Dados consolidados não encontrados no XCom")
        
        # Extrair dados consolidados
        extracted_data = consolidated_data['data']
        
        # Transformar com separação por business unit
        transformed_data = transform_produtos_by_business_unit(extracted_data, business_unit)
        tb_produtos = transformed_data.get('tb_produtos', pd.DataFrame())

        # Aplicar preparação adicional para serialização se necessário
        from salesforce_integration.data_transformers import _prepare_dataframe_for_serialization
        tb_produtos = _prepare_dataframe_for_serialization(tb_produtos)

        products_result = {
            'success': True,
            'transformed_data': {'tb_produtos': tb_produtos},
            'total_records': len(tb_produtos) if hasattr(tb_produtos, '__len__') else 0,
            'table_name': 'tb_produtos',
            'business_unit': business_unit
        }
        
        # Salvar resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='produtos_transformation_result', value=products_result)
            
        logging.info(f"✅ Transformação Produtos {business_unit}: {products_result['total_records']} registros")
        
        # Retornar apenas resumo (não o DataFrame completo)
        return {
            'success': True,
            'total_records': products_result['total_records'],
            'table_name': 'tb_produtos',
            'business_unit': business_unit
        }
        
    except Exception as e:
        logging.error(f"❌ Erro na transformação de produtos para {business_unit}: {e}")
        raise

def airflow_transform_clientes_task(**context):
    """Wrapper para transformação clientes com suporte a unidades de negócio"""
    import pandas as pd
    
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    business_unit = adapter.business_unit
    
    # Usar transformação específica por business unit
    from salesforce_integration.business_unit_transformers import transform_clientes_by_business_unit
    
    try:
        # Recuperar dados consolidados
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_table_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
            
        if consolidated_data is None:
            logging.error("❌ Dados consolidados não encontrados no XCom")
            raise ValueError("Dados consolidados não encontrados no XCom")
        
        # Extrair dados consolidados
        extracted_data = consolidated_data['data']
        
        # Transformar com separação por business unit
        transformed_data = transform_clientes_by_business_unit(extracted_data, business_unit)
        tb_clientes = transformed_data.get('tb_clientes', pd.DataFrame())

        # Aplicar preparação adicional para serialização se necessário
        from salesforce_integration.data_transformers import _prepare_dataframe_for_serialization
        tb_clientes = _prepare_dataframe_for_serialization(tb_clientes)

        clients_result = {
            'success': True,
            'transformed_data': {'tb_clientes': tb_clientes},
            'total_records': len(tb_clientes) if hasattr(tb_clientes, '__len__') else 0,
            'table_name': 'tb_clientes',
            'business_unit': business_unit
        }
        
        # Salvar resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='clientes_transformation_result', value=clients_result)
            
        logging.info(f"✅ Transformação Clientes {business_unit}: {clients_result['total_records']} registros")
        
        # Retornar apenas resumo (não o DataFrame completo)
        return {
            'success': True,
            'total_records': clients_result['total_records'],
            'table_name': 'tb_clientes',
            'business_unit': business_unit
        }
        
    except Exception as e:
        logging.error(f"❌ Erro na transformação de clientes para {business_unit}: {e}")
        raise

def airflow_transform_leads_task(**context):
    """Wrapper para transformação leads com suporte a unidades de negócio"""
    import pandas as pd
    
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    business_unit = adapter.business_unit
    
    # Usar transformação específica por business unit
    from salesforce_integration.business_unit_transformers import transform_leads_by_business_unit
    
    try:
        # Recuperar dados consolidados
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_table_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
            
        if consolidated_data is None:
            logging.error("❌ Dados consolidados não encontrados no XCom")
            raise ValueError("Dados consolidados não encontrados no XCom")
        
        # Extrair dados consolidados
        extracted_data = consolidated_data['data']
        
        # Transformar com separação por business unit
        transformed_data = transform_leads_by_business_unit(extracted_data, business_unit)
        tb_leads = transformed_data.get('tb_leads', pd.DataFrame())

        # Aplicar preparação adicional para serialização se necessário
        from salesforce_integration.data_transformers import _prepare_dataframe_for_serialization
        tb_leads = _prepare_dataframe_for_serialization(tb_leads)

        leads_result = {
            'success': True,
            'transformed_data': {'tb_leads': tb_leads},
            'total_records': len(tb_leads) if hasattr(tb_leads, '__len__') else 0,
            'table_name': 'tb_leads',
            'business_unit': business_unit
        }
        
        # Salvar resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='leads_transformation_result', value=leads_result)
            
        logging.info(f"✅ Transformação Leads {business_unit}: {leads_result['total_records']} registros")
        
        # Retornar apenas resumo (não o DataFrame completo)
        return {
            'success': True,
            'total_records': leads_result['total_records'],
            'table_name': 'tb_leads',
            'business_unit': business_unit
        }
        
    except Exception as e:
        logging.error(f"❌ Erro na transformação de leads para {business_unit}: {e}")
        raise

def airflow_transform_propostas_task(**context):
    """Wrapper para transformação propostas com suporte a unidades de negócio"""
    import pandas as pd
    
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    business_unit = adapter.business_unit
    
    # Usar transformação específica por business unit
    from salesforce_integration.business_unit_transformers import transform_propostas_by_business_unit
    
    try:
        # Recuperar dados consolidados
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_table_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
            
        if consolidated_data is None:
            logging.error("❌ Dados consolidados não encontrados no XCom")
            raise ValueError("Dados consolidados não encontrados no XCom")
        
        # Extrair dados consolidados
        extracted_data = consolidated_data['data']
        
        # Transformar com separação por business unit
        transformed_data = transform_propostas_by_business_unit(extracted_data, business_unit)
        tb_propostas = transformed_data.get('tb_propostas', pd.DataFrame())

        # Aplicar preparação adicional para serialização se necessário
        from salesforce_integration.data_transformers import _prepare_dataframe_for_serialization
        tb_propostas = _prepare_dataframe_for_serialization(tb_propostas)

        proposals_result = {
            'success': True,
            'transformed_data': {'tb_propostas': tb_propostas},
            'total_records': len(tb_propostas) if hasattr(tb_propostas, '__len__') else 0,
            'table_name': 'tb_propostas',
            'business_unit': business_unit
        }
        
        # Salvar resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='propostas_transformation_result', value=proposals_result)
            
        logging.info(f"✅ Transformação Propostas {business_unit}: {proposals_result['total_records']} registros")
        
        # Retornar apenas resumo (não o DataFrame completo)
        return {
            'success': True,
            'total_records': proposals_result['total_records'],
            'table_name': 'tb_propostas',
            'business_unit': business_unit
        }
        
    except Exception as e:
        logging.error(f"❌ Erro na transformação de propostas para {business_unit}: {e}")
        raise

# =============================================================================
# WRAPPERS DE CARREGAMENTO
# =============================================================================

def airflow_load_produtos_parallel_task(**context):
    """Wrapper para carregamento produtos com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_load_produtos_parallel_task as original_func
    return original_func(**context)

def airflow_load_clientes_parallel_task(**context):
    """Wrapper para carregamento clientes com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_load_clientes_parallel_task as original_func
    return original_func(**context)

def airflow_load_leads_parallel_task(**context):
    """Wrapper para carregamento leads com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_load_leads_parallel_task as original_func
    return original_func(**context)

def airflow_load_propostas_parallel_task(**context):
    """Wrapper para carregamento propostas com suporte a unidades de negócio"""
    adapter = create_business_unit_adapter(**context)
    patch_config_for_business_unit(adapter)
    
    from salesforce_integration.etl_main import airflow_load_propostas_parallel_task as original_func
    return original_func(**context)

