"""
Base DAG Factory - Fábrica de DAGs para Unidades de Negócio
Arquitetura sustentável para criar DAGs específicas por unidade de negócio.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
import logging
import importlib

class BaseDagFactory:
    """Fábrica base para criar DAGs específicas por unidade de negócio"""
    
    def __init__(self, business_unit: str, config: Dict[str, Any]):
        self.business_unit = business_unit
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Validação básica
        self._validate_config()
    
    def _validate_config(self):
        """Valida configuração da unidade de negócio"""
        required_keys = [
            'salesforce_config', 'data_extensions', 'sources', 
            'dag_config', 'extraction_tasks', 'transformation_tasks', 'load_tasks'
        ]
        
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Configuração obrigatória ausente: {key}")
    
    def _import_callable(self, callable_path: str):
        """Importa função a partir de string de caminho"""
        try:
            module_path, func_name = callable_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            return getattr(module, func_name)
        except Exception as e:
            self.logger.error(f"Erro ao importar {callable_path}: {e}")
            # Retorna função dummy em caso de erro
            def dummy(**context):
                raise ImportError(f"Não foi possível importar {callable_path}")
            return dummy
    
    def create_dag(self) -> DAG:
        """Cria DAG específica para a unidade de negócio"""
        dag_config = self.config['dag_config']
        
        # Configurações padrão do Airflow
        default_args = {
            'owner': f'etl_team_{self.business_unit}',
            'depends_on_past': False,
            'start_date': datetime(2025, 1, 1),
            'email_on_failure': False,
            'email_on_retry': False,
            'retries': 2,
            'retry_delay': timedelta(minutes=3),
            'execution_timeout': timedelta(hours=2),
        }
        
        # Sobrescreve com configurações específicas
        default_args.update(dag_config.get('default_args', {}))
        
        # Cria DAG
        dag = DAG(
            dag_id=f'INTEGRACAO-SALESFORCE-{self.business_unit.upper()}',
            default_args=default_args,
            description=f'ETL Pipeline {self.business_unit.title()} - Ultra-Paralelo por Tabela Individual',
            schedule_interval=dag_config.get('schedule_interval', '0 8 * * *'),
            catchup=False,
            max_active_runs=1,
            doc_md=self._generate_dag_documentation(),
        )
        
        # Cria operadores
        self._create_dag_operators(dag)
        
        # Define dependências
        self._set_dag_dependencies(dag)
        
        return dag
    
    def _generate_dag_documentation(self) -> str:
        """Gera documentação da DAG"""
        sources = ', '.join(self.config['sources'])
        extensions = ', '.join(self.config['data_extensions'].keys())
        
        return f"""
# ETL Salesforce Marketing Cloud - {self.business_unit.title()}

Pipeline ETL ultra-paralelo para unidade de negócio **{self.business_unit.title()}**.

## Fontes de Dados
{sources}

## Data Extensions
{extensions}

## Arquitetura
- **Extração**: Paralela por tabela individual
- **Transformação**: Paralela por tabela final
- **Carregamento**: Paralelo por Data Extension

## Modo de Saída
Configurável via `SALESFORCE_OUTPUT_MODE`:
- `salesforce`: Carrega no Salesforce Marketing Cloud
- `csv`: Gera arquivos CSV para revisão
        """
    
    def _create_dag_operators(self, dag: DAG):
        """Cria todos os operadores da DAG"""
        # Operadores de início e fim
        dag.start_pipeline = DummyOperator(
            task_id='start_pipeline',
            dag=dag,
            doc_md=f"Início do pipeline ETL {self.business_unit}"
        )
        
        dag.end_pipeline = DummyOperator(
            task_id='end_pipeline',
            dag=dag,
            doc_md=f"Fim do pipeline ETL {self.business_unit}"
        )
        
        # Operadores de extração
        dag.extraction_tasks = {}
        for task_name, task_config in self.config['extraction_tasks'].items():
            callable_func = self._import_callable(task_config['callable'])
            
            dag.extraction_tasks[task_name] = PythonOperator(
                task_id=f'extract_{task_name}',
                python_callable=callable_func,
                op_kwargs={'business_unit': self.business_unit},
                dag=dag,
                doc_md=task_config.get('doc_md', f'Extração {task_name}'),
                priority_weight=task_config.get('priority_weight', 5),
                execution_timeout=timedelta(minutes=task_config.get('timeout_minutes', 15)),
            )
        
        # Operador de consolidação
        consolidate_callable = self._import_callable(self.config.get('consolidate_callable'))
        dag.consolidate_task = PythonOperator(
            task_id='consolidate_table_extractions',
            python_callable=consolidate_callable,
            op_kwargs={'business_unit': self.business_unit},
            dag=dag,
            doc_md="Consolida dados de todas as extrações",
            priority_weight=11,
            execution_timeout=timedelta(minutes=5),
        )
        
        # Operadores de transformação
        dag.transformation_tasks = {}
        for task_name, task_config in self.config['transformation_tasks'].items():
            callable_func = self._import_callable(task_config['callable'])
            
            dag.transformation_tasks[task_name] = PythonOperator(
                task_id=f'transform_{task_name}',
                python_callable=callable_func,
                op_kwargs={'business_unit': self.business_unit},
                dag=dag,
                doc_md=task_config.get('doc_md', f'Transformação {task_name}'),
                priority_weight=task_config.get('priority_weight', 8),
                execution_timeout=timedelta(minutes=task_config.get('timeout_minutes', 10)),
            )
        
        # Operadores de carregamento
        dag.load_tasks = {}
        for task_name, task_config in self.config['load_tasks'].items():
            callable_func = self._import_callable(task_config['callable'])
            
            dag.load_tasks[task_name] = PythonOperator(
                task_id=f'load_{task_name}',
                python_callable=callable_func,
                op_kwargs={'business_unit': self.business_unit},
                dag=dag,
                doc_md=task_config.get('doc_md', f'Carregamento {task_name}'),
                priority_weight=task_config.get('priority_weight', 7),
                execution_timeout=timedelta(minutes=task_config.get('timeout_minutes', 20)),
            )
        

    
    def _set_dag_dependencies(self, dag: DAG):
        """Define dependências entre tarefas"""
        # Início → Extrações paralelas
        extraction_list = list(dag.extraction_tasks.values())
        dag.start_pipeline >> extraction_list
        
        # Extrações → Consolidação
        extraction_list >> dag.consolidate_task
        
        # Consolidação → Transformações paralelas
        transformation_list = list(dag.transformation_tasks.values())
        dag.consolidate_task >> transformation_list
        
        # Transformações → Carregamentos específicos
        for transform_name, transform_task in dag.transformation_tasks.items():
            # Mapeia transformação para carregamento correspondente
            load_task_name = transform_name  # Assume mesmo nome
            if load_task_name in dag.load_tasks:
                transform_task >> dag.load_tasks[load_task_name]
        
        # Carregamentos → Relatório → Fim
        load_list = list(dag.load_tasks.values())
        load_list >> dag.end_pipeline
    
    def get_source_filter(self) -> List[str]:
        """Retorna lista de fontes para esta unidade de negócio"""
        return self.config['sources']
    
    def get_data_extensions(self) -> Dict[str, Any]:
        """Retorna configurações das Data Extensions"""
        return self.config['data_extensions']
    
    def get_salesforce_config(self) -> Dict[str, Any]:
        """Retorna configurações do Salesforce para esta unidade"""
        return self.config['salesforce_config']

class DagRegistry:
    """Registro de DAGs por unidade de negócio"""
    
    _dags = {}
    
    @classmethod
    def register_dag(cls, business_unit: str, dag: DAG):
        """Registra uma DAG para uma unidade de negócio"""
        cls._dags[business_unit] = dag
    
    @classmethod
    def get_dag(cls, business_unit: str) -> Optional[DAG]:
        """Obtém DAG de uma unidade de negócio específica"""
        return cls._dags.get(business_unit)
    
    @classmethod
    def get_all_dags(cls) -> Dict[str, DAG]:
        """Retorna todas as DAGs registradas"""
        return cls._dags.copy()
    
    @classmethod
    def list_business_units(cls) -> List[str]:
        """Lista todas as unidades de negócio registradas"""
        return list(cls._dags.keys())