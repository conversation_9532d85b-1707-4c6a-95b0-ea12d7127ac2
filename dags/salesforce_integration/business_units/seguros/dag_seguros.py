"""
DAG Seguros - ETL Salesforce Marketing Cloud
Pipeline ETL específico para a unidade de negócio Seguros.
"""

import sys
import os
import logging

# Adicionar o diretório raiz ao path para importações
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from salesforce_integration.core.base_dag_factory import BaseDagFactory, DagRegistry
    from salesforce_integration.business_units.seguros.config import DAG_SEGUROS_CONFIG
    
    # Criar fábrica e DAG para seguros
    seguros_factory = BaseDagFactory('seguros', DAG_SEGUROS_CONFIG)
    dag_seguros = seguros_factory.create_dag()
    
    # Registrar DAG no registro global
    DagRegistry.register_dag('seguros', dag_seguros)
    
    # Exportar DAG para o Airflow descobrir
    globals()[dag_seguros.dag_id] = dag_seguros
    
    logging.info(f"✅ DAG Seguros criada com sucesso: {dag_seguros.dag_id}")
    
except ImportError as e:
    logging.error(f"❌ Erro ao importar módulos para DAG Seguros: {e}")
    
    # Fallback: criar DAG básica para evitar erro de importação
    from datetime import datetime, timedelta
    from airflow import DAG
    from airflow.operators.dummy_operator import DummyOperator
    
    dag_seguros_fallback = DAG(
        'INTEGRACAO-SALESFORCE-SEGUROS-FALLBACK',
        default_args={
            'owner': 'etl_team_seguros',
            'start_date': datetime(2025, 1, 1),
            'retries': 1,
            'retry_delay': timedelta(minutes=5),
        },
        description='DAG Seguros - Fallback (erro de importação)',
        schedule_interval=None,
        catchup=False,
    )
    
    fallback_task = DummyOperator(
        task_id='fallback_task',
        dag=dag_seguros_fallback,
    )
    
    globals()['INTEGRACAO-SALESFORCE-SEGUROS-FALLBACK'] = dag_seguros_fallback
    logging.warning("⚠️ DAG Seguros criada em modo fallback devido a erro de importação")

except Exception as e:
    logging.error(f"❌ Erro inesperado ao criar DAG Seguros: {e}")
    raise