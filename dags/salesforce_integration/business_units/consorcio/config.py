"""
Configurações Específicas - Unidade de Negócio Consórcio
"""

from salesforce_integration.config import SALESFORCE_CONFIG, RDSTATION_CONFIG

# =============================================================================
# CONFIGURAÇÕES SALESFORCE MARKETING CLOUD - CONSÓRCIO
# =============================================================================

SALESFORCE_CONSORCIO_CONFIG = {
    'client_id': 'bneq1jhgjhuhsekg68zchecl',  # Existente no config atual
    'client_secret': 'HcLyVCyU0ed48fKW6BGpGdff',  # Existente no config atual
    'auth_uri': SALESFORCE_CONFIG['auth_uri'],
    'rest_uri': SALESFORCE_CONFIG['rest_uri'],
    'timeout': SALESFORCE_CONFIG['timeout'],
    'batch_size': SALESFORCE_CONFIG['batch_size'],
    'rate_limit': SALESFORCE_CONFIG['rate_limit'],
    'retry_attempts': SALESFORCE_CONFIG['retry_attempts'],
    'retry_delay': SALESFORCE_CONFIG['retry_delay'],
    'exponential_backoff': SALESFORCE_CONFIG['exponential_backoff'],
    'polling_interval': SALESFORCE_CONFIG['polling_interval'],
    'max_polling_time': SALESFORCE_CONFIG['max_polling_time'],
}

# =============================================================================
# DATA EXTENSIONS - CONSÓRCIO (EXISTENTES)
# =============================================================================

DATA_EXTENSIONS_CONSORCIO = {
    'tb_clientes': {
        'external_key': '6E536388-F5A4-416D-B0DE-AABE229F33C1',  # Existente
        'required_fields': ['cnpjcpf', 'email'],
        'estimated_records': 73498,
        'batch_count': 37,
        'priority': 2,
    },
    'tb_leads': {
        'external_key': '7C8AEDB6-64E8-41FC-965F-A4C37DF0ABE0',  # Existente
        'required_fields': ['cnpjcpf', 'dt_simulacao'],
        'estimated_records': 0,
        'batch_count': 0,
        'priority': 3,
    },
    'tb_produtos': {
        'external_key': '19BEF707-C44F-4C3A-A9FB-53D2915258F1',  # Existente
        'required_fields': ['id_produto'],
        'estimated_records': 20502,
        'batch_count': 11,
        'priority': 1,
    },
    'tb_propostas': {
        'external_key': '9FC20C5F-04EC-4B86-9491-D649ECFACAA5',  # Existente
        'required_fields': ['idproposta', 'email'],
        'estimated_records': 533238,
        'batch_count': 267,
        'priority': 4,
    }
}

# =============================================================================
# FONTES DE DADOS - CONSÓRCIO
# =============================================================================

SOURCES_CONSORCIO = ['newcon', 'rdstation', 'orbbits']  # Exclui Quiver

# =============================================================================
# CONFIGURAÇÃO DA DAG - CONSÓRCIO
# =============================================================================

DAG_CONSORCIO_CONFIG = {
    'dag_config': {
        'schedule_interval': '0 8 * * *',  # 8h da manhã
        'default_args': {
            'email': ['<EMAIL>'],  # Email específico do consórcio
        }
    },
    'salesforce_config': SALESFORCE_CONSORCIO_CONFIG,
    'data_extensions': DATA_EXTENSIONS_CONSORCIO,
    'sources': SOURCES_CONSORCIO,
    
    # Tarefas de extração específicas do consórcio
    'extraction_tasks': {
        'newcon_clients': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_newcon_clients_individual_task',
            'doc_md': 'Extrai clientes NewCon para consórcio',
            'priority_weight': 10,
            'timeout_minutes': 15
        },
        'newcon_products': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_newcon_products_individual_task',
            'doc_md': 'Extrai produtos NewCon para consórcio',
            'priority_weight': 9,
            'timeout_minutes': 10
        },
        'newcon_leads': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_newcon_leads_individual_task',
            'doc_md': 'Extrai leads NewCon para consórcio',
            'priority_weight': 8,
            'timeout_minutes': 12
        },
        'newcon_proposals': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_newcon_proposals_individual_task',
            'doc_md': 'Extrai propostas NewCon para consórcio',
            'priority_weight': 7,
            'timeout_minutes': 25
        },
        'rdstation_leads': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_rdstation_leads_individual_task',
            'doc_md': 'Extrai leads RD Station para consórcio',
            'priority_weight': 6,
            'timeout_minutes': 15
        },
        'orbbits_origin': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_orbbits_origin_individual_task',
            'doc_md': 'Extrai dados origem Orbbits para consórcio',
            'priority_weight': 5,
            'timeout_minutes': 8
        },
        'orbbits_payments': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_orbbits_payments_individual_task',
            'doc_md': 'Extrai pagamentos Orbbits para consórcio',
            'priority_weight': 4,
            'timeout_minutes': 8
        },
        'orbbits_sales': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_orbbits_sales_individual_task',
            'doc_md': 'Extrai vendas Orbbits para consórcio',
            'priority_weight': 3,
            'timeout_minutes': 8
        },
        'orbbits_prices': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_orbbits_prices_individual_task',
            'doc_md': 'Extrai preços Orbbits para consórcio',
            'priority_weight': 2,
            'timeout_minutes': 8
        },
        'orbbits_proposals': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_orbbits_proposals_individual_task',
            'doc_md': 'Extrai propostas Orbbits para consórcio',
            'priority_weight': 1,
            'timeout_minutes': 8
        }
    },
    
    # Tarefa de consolidação
    'consolidate_callable': 'salesforce_integration.airflow_task_wrappers.airflow_consolidate_table_extractions_task',
    
    # Tarefas de transformação
    'transformation_tasks': {
        'produtos': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_transform_produtos_task',
            'doc_md': 'Transformação produtos para consórcio',
            'priority_weight': 10,
            'timeout_minutes': 8
        },
        'clientes': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_transform_clientes_task',
            'doc_md': 'Transformação clientes para consórcio',
            'priority_weight': 9,
            'timeout_minutes': 12
        },
        'leads': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_transform_leads_task',
            'doc_md': 'Transformação leads para consórcio',
            'priority_weight': 8,
            'timeout_minutes': 10
        },
        'propostas': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_transform_propostas_task',
            'doc_md': 'Transformação propostas para consórcio',
            'priority_weight': 7,
            'timeout_minutes': 15
        }
    },
    
    # Tarefas de carregamento
    'load_tasks': {
        'produtos': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_load_produtos_parallel_task',
            'doc_md': 'Carregamento produtos para consórcio',
            'priority_weight': 10,
            'timeout_minutes': 8
        },
        'clientes': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_load_clientes_parallel_task',
            'doc_md': 'Carregamento clientes para consórcio',
            'priority_weight': 9,
            'timeout_minutes': 12
        },
        'leads': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_load_leads_parallel_task',
            'doc_md': 'Carregamento leads para consórcio',
            'priority_weight': 8,
            'timeout_minutes': 45
        },
        'propostas': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_load_propostas_parallel_task',
            'doc_md': 'Carregamento propostas para consórcio',
            'priority_weight': 7,
            'timeout_minutes': 25
        }
    },
    

}