"""
DAG Consórcio - ETL Salesforce Marketing Cloud
Pipeline ETL específico para a unidade de negócio Consórcio.
"""

import sys
import os
import logging

# Adicionar o diretório raiz ao path para importações
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from salesforce_integration.core.base_dag_factory import BaseDagFactory, DagRegistry
    from salesforce_integration.business_units.consorcio.config import DAG_CONSORCIO_CONFIG
    
    # Criar fábrica e DAG para consórcio
    consorcio_factory = BaseDagFactory('consorcio', DAG_CONSORCIO_CONFIG)
    dag_consorcio = consorcio_factory.create_dag()
    
    # Registrar DAG no registro global
    DagRegistry.register_dag('consorcio', dag_consorcio)
    
    # Exportar DAG para o Airflow descobrir
    globals()[dag_consorcio.dag_id] = dag_consorcio
    
    logging.info(f"✅ DAG Consórcio criada com sucesso: {dag_consorcio.dag_id}")
    
except ImportError as e:
    logging.error(f"❌ Erro ao importar módulos para DAG Consórcio: {e}")
    
    # Fallback: criar DAG básica para evitar erro de importação
    from datetime import datetime, timedelta
    from airflow import DAG
    from airflow.operators.dummy_operator import DummyOperator
    
    dag_consorcio_fallback = DAG(
        'INTEGRACAO-SALESFORCE-CONSORCIO-FALLBACK',
        default_args={
            'owner': 'etl_team_consorcio',
            'start_date': datetime(2025, 1, 1),
            'retries': 1,
            'retry_delay': timedelta(minutes=5),
        },
        description='DAG Consórcio - Fallback (erro de importação)',
        schedule_interval=None,
        catchup=False,
    )
    
    fallback_task = DummyOperator(
        task_id='fallback_task',
        dag=dag_consorcio_fallback,
    )
    
    globals()['INTEGRACAO-SALESFORCE-CONSORCIO-FALLBACK'] = dag_consorcio_fallback
    logging.warning("⚠️ DAG Consórcio criada em modo fallback devido a erro de importação")

except Exception as e:
    logging.error(f"❌ Erro inesperado ao criar DAG Consórcio: {e}")
    raise